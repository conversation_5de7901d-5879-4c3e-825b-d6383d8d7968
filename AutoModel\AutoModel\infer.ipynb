{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c64509de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING:tensorflow:From d:\\Anaconda\\envs\\cuda121_torch250_py311\\Lib\\site-packages\\keras\\src\\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.\n", "\n"]}], "source": ["from model_zoo import *\n", "from utils import *\n", "import os\n", "import time\n", "import datetime\n", "import gc\n", "import yaml\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import torch\n", "import torch.nn as nn\n", "from torch.cuda import mem_get_info\n", "from torch.utils.data import DataLoader\n", "from pprint import pprint\n", "import schedulefree\n", "from time import sleep\n", "from torch.amp import autocast, GradScaler \n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 2, "id": "88cf8440", "metadata": {}, "outputs": [], "source": ["INPUT_PATH = r'D:\\CODE\\game\\yelu\\AutoModel\\run\\exp_20250521_122411'\n", "TTA = True"]}, {"cell_type": "code", "execution_count": 3, "id": "4b2de594", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded config:\n", "{'backbone': {'params': {'classes': 1,\n", "                         'decoder_attention_type': 'scse',\n", "                         'decoder_channels': [512, 256, 128, 64, 32],\n", "                         'decoder_interpolation': 'nearest',\n", "                         'encoder_depth': 5,\n", "                         'encoder_name': 'resnet34',\n", "                         'encoder_weights': 'imagenet',\n", "                         'in_channels': 5},\n", "              'type': 'smp.UnetPlusPlus'},\n", " 'batch_size': 64,\n", " 'data_path': ['D:\\\\CODE\\\\game\\\\yelu\\\\openfwi_float16_1',\n", "               'D:\\\\CODE\\\\game\\\\yelu\\\\openfwi_float16_2'],\n", " 'es_epochs': 5,\n", " 'extractor': {'params': {'kernel_size': [4, 1], 'stride': [4, 1]},\n", "               'type': 'nn.AvgPool2d'},\n", " 'max_epochs': 100,\n", " 'optimizer': {'lr': 0.00025, 'weight_decay': 0.001},\n", " 'output_path': 'D:\\\\CODE\\\\game\\\\yelu\\\\AutoModel\\\\run\\\\exp',\n", " 'print_freq': 100,\n", " 'read_weights': None,\n", " 'scheduler': {'params': {'factor': 0.316227766, 'patience': 1}},\n", " 'seed': 100,\n", " 'size_type': 'resize',\n", " 'target_size': [224, 224],\n", " 'train_frac': 1,\n", " 'use_ema': True,\n", " 'use_schedulerfree': <PERSON><PERSON><PERSON>,\n", " 'valid_frac': 16}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 20/20 [00:00<00:00, 167.18it/s]\n", "Validating: 100%|██████████| 79/79 [01:34<00:00,  1.20s/batch, loss=72.5625  loss_TTA: 70.6250]  "]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Final validation loss: 50.4661\n", "Final validation loss_TTA: 48.0456\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from tqdm import tqdm\n", "\n", "\n", "with open(os.path.join(INPUT_PATH, 'config.yaml'), 'r') as f:\n", "    config = yaml.safe_load(f)\n", "print(\"Loaded config:\")\n", "pprint(config)\n", "base_out = config['output_path']\n", "seed_everything(config['seed'])\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "model = build_model_from_cfg(os.path.join(INPUT_PATH, 'config.yaml'))\n", "model.load_state_dict(torch.load(os.path.join(INPUT_PATH, 'best_model.pth'), weights_only=True,  map_location=device))\n", "model.to(device)\n", "model.eval();\n", "\n", "dsvalid = CustomDataset(mode=\"valid\")\n", "dlvalid = DataLoader(\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    batch_size=2*config[\"batch_size\"],\n", "    shuffle=False,\n", "    # pin_memory=True,\n", "    drop_last=False,\n", "    num_workers=0,\n", "    persistent_workers=False,\n", ")\n", "\n", "criterion = nn.L1Loss()\n", "\n", "valid_losses = []\n", "valid_losses_TTA = []\n", "pbar = tqdm(dlvalid, desc=\"Validating\", unit=\"batch\")\n", "\n", "for inputs, targets in pbar:\n", "    inputs = inputs.to(device)\n", "    targets = targets.to(device)\n", "\n", "    with torch.inference_mode(), torch.autocast(device_type=\"cuda\"):\n", "        if TTA:\n", "            outputs1 = model(inputs)\n", "            outputs2 = torch.flip(model(torch.flip(inputs, dims=[-3, -1])), dims=[-1])\n", "        else:\n", "            outputs = model(inputs)\n", "        \n", "    if TTA:\n", "        loss_TTA = criterion((outputs1+outputs2)/2, targets)\n", "        loss = criterion(outputs1, targets)\n", "        valid_losses_TTA.append(loss_TTA.item())\n", "        valid_losses.append(loss.item())\n", "        pbar.set_postfix(loss=f\"{loss.item():.4f}  loss_TTA: {loss_TTA.item():.4f}\")\n", "    else:\n", "        loss = criterion(outputs, targets)\n", "        valid_losses.append(loss.item())\n", "        pbar.set_postfix(loss=f\"{loss.item():.4f}\")\n", "    \n", "\n", "val_loss = np.mean(valid_losses)\n", "print(f\"\\nFinal validation loss: {val_loss:.4f}\")\n", "if TTA:\n", "    val_loss_TTA = np.mean(valid_losses_TTA)\n", "    print(f\"Final validation loss_TTA: {val_loss_TTA:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "247ec822", "metadata": {}, "outputs": [], "source": ["# Inference\n", "\n", "import csv\n", "from pathlib import Path\n", "import os\n", "from tqdm import tqdm\n", "\n", "t0 = time.time()\n", "\n", "test_files = list(Path(r\"D:\\CODE\\game\\yelu\\test\").glob(\"*.npy\"))\n", "x_cols = [f\"x_{i}\" for i in range(1, 70, 2)]\n", "fieldnames = [\"oid_ypos\"] + x_cols\n", "ds = TestDataset(test_files)\n", "dl = DataLoader(ds, batch_size=2*config[\"batch_size\"], num_workers=0, pin_memory=False)\n", "\n", "with open(os.path.join(INPUT_PATH, \"submission.csv\"), \"wt\", newline=\"\") as csvfile:\n", "    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)\n", "    writer.writeheader()\n", "\n", "    for inputs, oids_test in tqdm(dl):\n", "        inputs = inputs.to(device)\n", "        with torch.inference_mode():\n", "            with torch.autocast(device_type=\"cuda\"):\n", "                \n", "                if TTA:\n", "                    outputs = model(inputs)*0.5\n", "                    outputs += torch.flip(model(torch.flip(inputs, dims=[-3, -1])), dims=[-1])*0.5\n", "                else:\n", "                    outputs = model(inputs)\n", "                    \n", "\n", "        y_preds = outputs[:, 0].cpu().numpy()\n", "\n", "        for y_pred, oid_test in zip(y_preds, oids_test):\n", "            for y_pos in range(70):\n", "                row = dict(zip(x_cols, [y_pred[y_pos, x_pos] for x_pos in range(1, 70, 2)]))\n", "                row[\"oid_ypos\"] = f\"{oid_test}_y_{y_pos}\"\n", "\n", "                writer.writerow(row)\n", "\n", "t1 = format_time(time.time() - t0)\n", "print(f\"Inference Time: {t1}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b06f5499", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "cuda121_torch250_py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}