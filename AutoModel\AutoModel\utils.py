import glob
import importlib
import pandas as pd
from tqdm import tqdm
import yaml
import torch.nn as nn
import torch
import segmentation_models_pytorch 
import monai
import model_zoo
import torch.nn.functional as F

ALIASES = {
    "smp": segmentation_models_pytorch,
    "monai": monai,
    "model": model_zoo,   
    "nn": torch.nn,
}

class CompositeModel_centerpad(nn.Module):
    def __init__(self, extractor: nn.Module, backbone: nn.Module, target_size=[70,70]):
        super().__init__()
        self.extractor = extractor
        self.backbone = backbone
        self.target_size = target_size

    def _pad_or_crop(self, x, target_h=70, target_w=70):
        """Pads or crops input tensor x to target height and width."""
        _, _, h, w = x.shape
        if h == target_h and w == target_w:
            return x
        # Pad Height if needed
        if h < target_h:
            pad_top = (target_h - h) // 2
            pad_bottom = target_h - h - pad_top
            x = F.pad(x, (0, 0, pad_top, pad_bottom))  # Pad height only
            h = target_h
        # Pad Width if needed
        if w < target_w:
            pad_left = (target_w - w) // 2
            pad_right = target_w - w - pad_left
            x = F.pad(x, (pad_left, pad_right, 0, 0))  # Pad width only
            w = target_w
        # Crop Height if needed
        if h > target_h:
            crop_top = (h - target_h) // 2
            # Use slicing to crop
            x = x[:, :, crop_top : crop_top + target_h, :]
            h = target_h
        # Crop Width if needed
        if w > target_w:
            crop_left = (w - target_w) // 2
            x = x[:, :, :, crop_left : crop_left + target_w]
            w = target_w
        return x

    def forward(self, x):
        x = self.extractor(x)
        x = self._pad_or_crop(x, *self.target_size)
        x = self.backbone(x)
        if x.shape[2] == 70 and x.shape[3] == 70:
            return x * 1000.0 + 1500.0
        else:
            return self._pad_or_crop(x, 70, 70) * 1000.0 + 1500.0

class CompositeModel_Resize(nn.Module):
    def __init__(self, extractor: nn.Module, backbone: nn.Module, target_size=[70,70]):
        super().__init__()
        self.extractor = extractor
        self.backbone = backbone
        self.target_size = target_size
    def forward(self, x):
        x = self.extractor(x)
        if self.target_size[0] < x.shape[2] or self.target_size[1] < x.shape[3]:
            x = F.interpolate(x, size=self.target_size, mode='area')
        else:
            x = F.interpolate(x, size=self.target_size, mode='bilinear')
        x = self.backbone(x)
        if x.shape[2] == 70 and x.shape[3] == 70:
            return x * 1000.0 + 1500.0
        elif x.shape[2] > 70 or x.shape[3] > 70:
            return F.interpolate(x, size=[70,70], mode='area') * 1000.0 + 1500.0
        else:
            return F.interpolate(x, size=[70,70], mode='bilinear') * 1000.0 + 1500.0
        
        
def resolve_class(full_name: str):
    module_name, class_name = full_name.rsplit(".", 1)
    if module_name in ALIASES:
        module = ALIASES[module_name]
    else:
        module = importlib.import_module(module_name)
    return getattr(module, class_name)

def build_model_from_cfg(cfg_path: str) -> nn.Module:
    with open(cfg_path, 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)

    ext_fullname = cfg["extractor"]["type"]
    ExtClass = resolve_class(ext_fullname)
    ext_params = cfg["extractor"].get("params", {})
    extractor: nn.Module = ExtClass(**ext_params)

    bb_fullname = cfg["backbone"]["type"]
    BackBoneClass = resolve_class(bb_fullname)
    bb_params = cfg["backbone"].get("params", {})

    if hasattr(extractor, "out_channels"):
        bb_params["in_channels"] = extractor.out_channels

    backbone: nn.Module = BackBoneClass(**bb_params)
    if cfg["size_type"] == 'resize':
        return CompositeModel_Resize(extractor, backbone, cfg["target_size"])
    elif cfg["size_type"] =='centerpad':
        return CompositeModel_centerpad(extractor, backbone, cfg["target_size"])
    else:
        raise ValueError("Invalid size_type")

import os
import random
import numpy as np
from pathlib import Path
from torch.utils.data import Dataset, DataLoader

def inputs_files_to_output_files(input_files):
    return [
        Path(str(f).replace('seis', 'vel').replace('data', 'model'))
        for f in input_files
    ]


def get_train_files(data_path):

    all_inputs = [
        f
        for f in
        Path(data_path).rglob('*.npy')
        if ('seis' in f.stem) or ('data' in f.stem)
    ]

    all_outputs = inputs_files_to_output_files(all_inputs)

    assert all(f.exists() for f in all_outputs)

    return all_inputs, all_outputs


class SeismicDataset(Dataset):
    def __init__(self, inputs_files, output_files, mode, n_examples_per_file=500):
        assert len(inputs_files) == len(output_files)
        self.inputs_files = inputs_files
        self.output_files = output_files
        self.n_examples_per_file = n_examples_per_file
        self.mode= mode

    def __len__(self):
        return len(self.inputs_files) * self.n_examples_per_file

    def __getitem__(self, idx):
        # Calculate file offset and sample offset within file
        file_idx = idx // self.n_examples_per_file
        sample_idx = idx % self.n_examples_per_file

        x = np.load(self.inputs_files[file_idx], mmap_mode='r')[sample_idx]
        y = np.load(self.output_files[file_idx], mmap_mode='r')[sample_idx]

        # Random Flips
        if self.mode == "train":
            if random.random() < 0.5:
                x= x[::-1, :, ::-1]
                y= y[:, ::-1]
        try:
            return x.copy(), y.copy()
        finally:
            del x, y
            
class CustomDataset(torch.utils.data.Dataset):
    def __init__(
        self, 
        subsample = None,
        mode = "train", 
        rank = 0,
    ):
        self.subsample = subsample
        self.mode = mode
        self.rank = rank
        self.data, self.labels, self.records = self.load_metadata()

    def load_metadata(self, ):

        # Select rows
        df= pd.read_csv(r"D:\CODE\game\yelu\folds.csv")
        if self.subsample is not None:
            df= df.groupby(["dataset", "fold"]).head(self.cfg.subsample)

        if self.mode == "train":
            df= df[df["fold"] != 0]
        else:
            df= df[df["fold"] == 0]

        
        data = []
        labels = []
        records = []
        mmap_mode = "r"

        for idx, row in tqdm(df.iterrows(), total=len(df), disable = self.rank != 0):
            row= row.to_dict()

            # Hacky way to get exact file name
            p1 = os.path.join(r"D:\CODE\game\yelu\openfwi_float16_1", row["data_fpath"])
            p2 = os.path.join(r"D:\CODE\game\yelu\openfwi_float16_1", row["data_fpath"].split("/")[0], "*", row["data_fpath"].split("/")[-1])
            p3 = os.path.join(r"D:\CODE\game\yelu\openfwi_float16_2", row["data_fpath"])
            p4 = os.path.join(r"D:\CODE\game\yelu\openfwi_float16_2", row["data_fpath"].split("/")[0], "*", row["data_fpath"].split("/")[-1])
            farr= glob.glob(p1) + glob.glob(p2) + glob.glob(p3) + glob.glob(p4)
        
            # Map to lbl fpath
            farr= farr[0]
            flbl= farr.replace('seis', 'vel').replace('data', 'model')
            
            # Load
            arr= np.load(farr, mmap_mode=mmap_mode)
            lbl= np.load(flbl, mmap_mode=mmap_mode)

            # Append
            data.append(arr)
            labels.append(lbl)
            records.append(row["dataset"])

        return data, labels, records

    def __getitem__(self, idx):
        row_idx= idx // 500
        col_idx= idx % 500

        d= self.records[row_idx]
        x= self.data[row_idx][col_idx, ...]
        y= self.labels[row_idx][col_idx, ...]

        # Augs 
        if self.mode == "train":
            
            # Temporal flip
            if np.random.random() < 0.5:
                x= x[::-1, :, ::-1]
                y= y[..., ::-1]

        x= x.copy()
        y= y.copy()
        
        return x, y

    def __len__(self, ):
        return len(self.records) * 500

class TestDataset(Dataset):
    def __init__(self, test_files):
        self.test_files = test_files


    def __len__(self):
        return len(self.test_files)


    def __getitem__(self, i):
        test_file = self.test_files[i]

        return np.load(test_file), test_file.stem
    
import datetime

def format_time(elapsed):
    """Take a time in seconds and return a string hh:mm:ss."""
    elapsed_rounded = int(round((elapsed)))
    return str(datetime.timedelta(seconds=elapsed_rounded))

def seed_everything(
    seed_value: int
) -> None:
    """
    Controlling a unified seed value for Python, NumPy, and PyTorch (CPU, GPU).

    Parameters:
    ----------
    seed_value : int
        The unified random seed value.
    """
    random.seed(seed_value) # Python
    np.random.seed(seed_value) # cpu vars
    torch.manual_seed(seed_value) # cpu  vars    
    if torch.cuda.is_available(): 
        torch.cuda.manual_seed(seed_value)
        torch.cuda.manual_seed_all(seed_value) # gpu vars
    if torch.backends.cudnn.is_available:
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

from copy import deepcopy
import torch
import torch.nn as nn

class ModelEMA(nn.Module):
    def __init__(self, model, decay=0.99, device=None):
        super().__init__()
        self.module = deepcopy(model)
        self.module.eval()
        self.decay = decay
        self.device = device
        if self.device is not None:
            self.module.to(device=device)

    def _update(self, model, update_fn):
        with torch.no_grad():
            for ema_v, model_v in zip(self.module.state_dict().values(), model.state_dict().values()):
                if self.device is not None:
                    model_v = model_v.to(device=self.device)
                ema_v.copy_(update_fn(ema_v, model_v))

    def update(self, model):
        self._update(model, update_fn=lambda e, m: self.decay * e + (1. - self.decay) * m)

    def set(self, model):
        self._update(model, update_fn=lambda e, m: m)